<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class OrderFile extends Model
{
    use HasFactory;

    protected $fillable = [
        'printing_order_id',
        'original_name',
        'file_name',
        'file_path',
        'file_size',
        'mime_type',
        'file_type',
        'dimensions',
        'dpi',
        'is_approved',
        'notes',
        'uploaded_by',
    ];

    protected function casts(): array
    {
        return [
            'dimensions' => 'array',
            'is_approved' => 'boolean',
        ];
    }

    // File type constants
    const TYPE_ARTWORK = 'artwork';
    const TYPE_REFERENCE = 'reference';
    const TYPE_PROOF = 'proof';

    /**
     * Get the order that owns the file
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(PrintingOrder::class, 'printing_order_id');
    }

    /**
     * Get the user who uploaded the file
     */
    public function uploader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get all file types
     */
    public static function getFileTypes(): array
    {
        return [
            self::TYPE_ARTWORK => 'Artwork',
            self::TYPE_REFERENCE => 'Reference',
            self::TYPE_PROOF => 'Proof',
        ];
    }

    /**
     * Get file type label
     */
    public function getFileTypeLabelAttribute()
    {
        return self::getFileTypes()[$this->file_type] ?? $this->file_type;
    }

    /**
     * Get formatted file size
     */
    public function getFormattedFileSizeAttribute()
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get file URL
     */
    public function getFileUrlAttribute()
    {
        return Storage::url($this->file_path);
    }

    /**
     * Check if file is an image
     */
    public function getIsImageAttribute()
    {
        return in_array($this->mime_type, [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/svg+xml',
            'image/webp'
        ]);
    }

    /**
     * Check if file meets DPI requirements
     */
    public function meetsDpiRequirements(): bool
    {
        // For print quality, we typically require 300 DPI minimum
        return $this->dpi >= 300;
    }

    /**
     * Get DPI status
     */
    public function getDpiStatusAttribute()
    {
        if (!$this->dpi) {
            return 'unknown';
        }
        
        if ($this->dpi >= 300) {
            return 'good';
        } elseif ($this->dpi >= 150) {
            return 'acceptable';
        } else {
            return 'poor';
        }
    }

    /**
     * Delete file from storage when model is deleted
     */
    protected static function boot()
    {
        parent::boot();
        
        static::deleting(function ($file) {
            if (Storage::exists($file->file_path)) {
                Storage::delete($file->file_path);
            }
        });
    }
}
