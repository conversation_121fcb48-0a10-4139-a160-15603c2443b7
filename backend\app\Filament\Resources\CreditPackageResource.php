<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CreditPackageResource\Pages;
use App\Filament\Resources\CreditPackageResource\RelationManagers;
use App\Models\CreditPackage;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CreditPackageResource extends Resource
{
    protected static ?string $model = CreditPackage::class;

    protected static ?string $navigationIcon = 'heroicon-o-gift';
    protected static ?string $navigationGroup = 'Settings';
    protected static ?string $navigationLabel = 'Credit Packages';
    protected static ?string $modelLabel = 'Credit Package';
    protected static ?string $pluralModelLabel = 'Credit Packages';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Package Details')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->columnSpanFull(),

                        Forms\Components\Textarea::make('description')
                            ->rows(3)
                            ->columnSpanFull(),

                        Forms\Components\TextInput::make('price')
                            ->required()
                            ->numeric()
                            ->prefix('RM')
                            ->step(0.01)
                            ->minValue(0),

                        Forms\Components\TextInput::make('credit_amount')
                            ->required()
                            ->numeric()
                            ->minValue(1)
                            ->suffix('credits'),

                        Forms\Components\TextInput::make('sort_order')
                            ->numeric()
                            ->default(0)
                            ->helperText('Lower numbers appear first'),

                        Forms\Components\Toggle::make('is_active')
                            ->default(true)
                            ->helperText('Only active packages are available for purchase'),
                    ])->columns(2),

                Forms\Components\Section::make('Features')
                    ->schema([
                        Forms\Components\Repeater::make('features')
                            ->schema([
                                Forms\Components\TextInput::make('feature')
                                    ->required()
                                    ->placeholder('e.g., Priority support, Extra downloads, etc.'),
                            ])
                            ->addActionLabel('Add Feature')
                            ->collapsible()
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('price')
                    ->money('MYR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('credit_amount')
                    ->suffix(' credits')
                    ->sortable(),

                Tables\Columns\TextColumn::make('price_per_credit')
                    ->label('Price per Credit')
                    ->money('MYR')
                    ->getStateUsing(fn ($record) => $record->price_per_credit),

                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('sort_order')
                    ->sortable(),

                Tables\Columns\TextColumn::make('features')
                    ->label('Features')
                    ->formatStateUsing(function ($state) {
                        if (is_array($state) && !empty($state)) {
                            // Handle both old format (array of strings) and new format (array of objects)
                            $features = [];
                            foreach ($state as $feature) {
                                if (is_array($feature) && isset($feature['feature'])) {
                                    $features[] = $feature['feature'];
                                } elseif (is_string($feature)) {
                                    $features[] = $feature;
                                }
                            }
                            return implode(', ', $features);
                        }
                        return 'No features';
                    })
                    ->limit(50)
                    ->tooltip(function ($state) {
                        if (is_array($state) && !empty($state)) {
                            $features = [];
                            foreach ($state as $feature) {
                                if (is_array($feature) && isset($feature['feature'])) {
                                    $features[] = $feature['feature'];
                                } elseif (is_string($feature)) {
                                    $features[] = $feature;
                                }
                            }
                            return implode("\n", $features);
                        }
                        return null;
                    })
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status')
                    ->boolean()
                    ->trueLabel('Active packages')
                    ->falseLabel('Inactive packages')
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sort_order');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCreditPackages::route('/'),
            'create' => Pages\CreateCreditPackage::route('/create'),
            'edit' => Pages\EditCreditPackage::route('/{record}/edit'),
        ];
    }
}
