<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CmsPage;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CmsPageController extends Controller
{
    /**
     * Get published CMS pages
     */
    public function index(Request $request): JsonResponse
    {
        $query = CmsPage::published()
            ->with(['creator:id,name', 'updater:id,name'])
            ->orderBy('published_at', 'desc');

        // Filter by featured if requested
        if ($request->boolean('featured')) {
            $query->featured();
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhere('meta_description', 'like', "%{$search}%");
            });
        }

        $perPage = $request->get('per_page', 15);
        $pages = $query->paginate($perPage);

        return response()->json([
            'pages' => $pages->items(),
            'pagination' => [
                'current_page' => $pages->currentPage(),
                'last_page' => $pages->lastPage(),
                'per_page' => $pages->perPage(),
                'total' => $pages->total(),
                'has_more_pages' => $pages->hasMorePages(),
            ]
        ]);
    }

    /**
     * Get a specific CMS page by slug
     */
    public function show(string $slug): JsonResponse
    {
        $page = CmsPage::published()
            ->with(['creator:id,name', 'updater:id,name'])
            ->where('slug', $slug)
            ->first();

        if (!$page) {
            return response()->json([
                'message' => 'Page not found'
            ], 404);
        }

        return response()->json([
            'page' => $page
        ]);
    }
}
