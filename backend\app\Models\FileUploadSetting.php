<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class FileUploadSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'description',
        'is_encrypted',
    ];

    protected function casts(): array
    {
        return [
            'is_encrypted' => 'boolean',
        ];
    }

    /**
     * Get the value attribute with automatic decryption if needed
     */
    public function getValueAttribute($value)
    {
        if ($this->is_encrypted && $value) {
            try {
                return Crypt::decryptString($value);
            } catch (\Exception $e) {
                return $value;
            }
        }

        // Cast value based on type
        return match ($this->type) {
            'boolean' => (bool) $value,
            'integer' => (int) $value,
            'decimal' => (float) $value,
            'json' => json_decode($value, true),
            default => $value,
        };
    }

    /**
     * Set the value attribute with automatic encryption if needed
     */
    public function setValueAttribute($value)
    {
        if ($this->is_encrypted && $value) {
            $this->attributes['value'] = Crypt::encryptString($value);
        } else {
            // Convert value to string for storage
            $this->attributes['value'] = match ($this->type) {
                'json' => json_encode($value),
                'boolean' => $value ? '1' : '0',
                default => (string) $value,
            };
        }
    }

    /**
     * Get a setting value by key
     */
    public static function get(string $key, $default = null)
    {
        $setting = static::where('key', $key)->first();

        if (!$setting) {
            return $default;
        }

        // Get the raw value from database to avoid the accessor
        $rawValue = $setting->getAttributes()['value'] ?? null;

        if (!$rawValue) {
            return $default;
        }

        // Handle decryption if needed
        if ($setting->is_encrypted) {
            try {
                $rawValue = Crypt::decryptString($rawValue);
            } catch (\Exception $e) {
                // If decryption fails, use raw value
            }
        }

        // Cast value based on type but ensure strings for form compatibility
        $value = match ($setting->type) {
            'boolean' => (bool) $rawValue,
            'integer' => (int) $rawValue,
            'decimal' => (float) $rawValue,
            'json' => $rawValue, // Return raw JSON string for forms
            default => $rawValue,
        };

        return $value;
    }

    /**
     * Get a setting value by key with proper type casting (for non-form usage)
     */
    public static function getTyped(string $key, $default = null)
    {
        $setting = static::where('key', $key)->first();

        if (!$setting) {
            return $default;
        }

        // Use the accessor to get properly typed value
        return $setting->value;
    }

    /**
     * Set a setting value by key
     */
    public static function set(string $key, $value, string $type = 'string', bool $isEncrypted = false): self
    {
        return static::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'type' => $type,
                'is_encrypted' => $isEncrypted,
            ]
        );
    }

    /**
     * Get default file upload settings
     */
    public static function getDefaults(): array
    {
        return [
            // File Format Settings
            'allowed_file_types' => ['pdf', 'ai', 'eps', 'png', 'jpg', 'jpeg', 'tiff', 'svg'],
            'max_file_size_mb' => 50,
            'max_total_upload_size_mb' => 200,
            'max_files_per_order' => 10,
            
            // DPI Settings
            'min_dpi_requirement' => 300,
            'dpi_warning_threshold' => 150,
            'enable_dpi_validation' => true,
            'auto_dpi_detection' => true,
            
            // Dimension Settings
            'min_width_px' => 100,
            'min_height_px' => 100,
            'max_width_px' => 10000,
            'max_height_px' => 10000,
            'enable_dimension_validation' => true,
            
            // Compression Settings
            'enable_auto_compression' => false,
            'compression_quality' => 85,
            'compression_threshold_mb' => 10,
            
            // Category-specific settings
            'category_specific_rules' => [],
        ];
    }
}
