<?php

namespace App\Filament\Resources\CreditTransactionResource\Pages;

use App\Filament\Resources\CreditTransactionResource;
use Filament\Actions;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\ViewRecord;

class ViewCreditTransaction extends ViewRecord
{
    protected static string $resource = CreditTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Transaction Details')
                    ->schema([
                        Infolists\Components\TextEntry::make('id')
                            ->label('Transaction ID'),
                        Infolists\Components\TextEntry::make('user.name')
                            ->label('User'),
                        Infolists\Components\TextEntry::make('type')
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                'purchase' => 'success',
                                'usage' => 'warning',
                                'refund' => 'danger',
                                'bonus' => 'info',
                                default => 'gray',
                            }),
                        Infolists\Components\TextEntry::make('credit_amount')
                            ->suffix(' credits')
                            ->color(fn ($state) => $state > 0 ? 'success' : 'danger'),
                        Infolists\Components\TextEntry::make('amount_paid')
                            ->money('MYR'),
                        Infolists\Components\TextEntry::make('payment_method')
                            ->badge(),
                        Infolists\Components\TextEntry::make('payment_status')
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                'completed' => 'success',
                                'pending' => 'warning',
                                'failed' => 'danger',
                                'refunded' => 'gray',
                                default => 'gray',
                            }),
                        Infolists\Components\TextEntry::make('payment_reference')
                            ->label('Payment Reference'),
                        Infolists\Components\TextEntry::make('description'),
                        Infolists\Components\TextEntry::make('processed_at')
                            ->dateTime(),
                        Infolists\Components\TextEntry::make('created_at')
                            ->dateTime(),
                    ])->columns(2),

                Infolists\Components\Section::make('Metadata')
                    ->schema([
                        Infolists\Components\TextEntry::make('metadata')
                            ->formatStateUsing(function ($state) {
                                if (is_array($state) && !empty($state)) {
                                    return json_encode($state, JSON_PRETTY_PRINT);
                                }
                                return 'No metadata available';
                            })
                            ->html()
                            ->columnSpanFull(),
                    ])
                    ->visible(fn ($record) => !empty($record->metadata)),
            ]);
    }
}
