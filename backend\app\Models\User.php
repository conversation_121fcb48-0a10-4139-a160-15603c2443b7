<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;

class User extends Authenticatable implements MustVerifyEmail, FilamentUser
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'bio',
        'avatar',
        'date_of_birth',
        'role',
        'is_active',
        'credit_balance',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'date_of_birth' => 'date',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Check if user can access Filament admin panel
     */
    public function canAccessPanel(Panel $panel): bool
    {
        return $this->role === 'admin' && $this->is_active;
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Get CMS pages created by this user
     */
    public function cmsPages(): HasMany
    {
        return $this->hasMany(CmsPage::class, 'created_by');
    }

    /**
     * Get CMS pages updated by this user
     */
    public function updatedCmsPages(): HasMany
    {
        return $this->hasMany(CmsPage::class, 'updated_by');
    }

    /**
     * Get email notifications for this user
     */
    public function emailNotifications(): HasMany
    {
        return $this->hasMany(EmailNotification::class);
    }

    /**
     * Get credit transactions for this user
     */
    public function creditTransactions(): HasMany
    {
        return $this->hasMany(CreditTransaction::class);
    }

    /**
     * Add credits to user balance
     */
    public function addCredits(int $amount, string $description = null): void
    {
        $this->increment('credit_balance', $amount);

        // Create transaction record
        $this->creditTransactions()->create([
            'type' => 'credit',
            'credit_amount' => $amount,
            'description' => $description ?? "Credit added: {$amount} credits",
            'payment_status' => 'completed',
            'processed_at' => now(),
        ]);
    }

    /**
     * Deduct credits from user balance
     */
    public function deductCredits(int $amount, string $description = null): bool
    {
        if ($this->credit_balance < $amount) {
            return false;
        }

        $this->decrement('credit_balance', $amount);

        // Create transaction record
        $this->creditTransactions()->create([
            'type' => 'usage',
            'credit_amount' => -$amount,
            'description' => $description ?? "Credit used: {$amount} credits",
            'payment_status' => 'completed',
            'processed_at' => now(),
        ]);

        return true;
    }

    /**
     * Check if user has enough credits
     */
    public function hasCredits(int $amount): bool
    {
        return $this->credit_balance >= $amount;
    }
}
