<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\FileUploadController;

Route::get('/', function () {
    return view('welcome');
});

// Admin file download routes (for Filament admin panel)
Route::middleware(['web'])->prefix('admin')->group(function () {
    Route::get('/orders/{orderId}/files/{fileId}/download', [FileUploadController::class, 'downloadFile'])
        ->name('admin.orders.files.download');
    Route::get('/orders/{orderId}/files/download-all', [FileUploadController::class, 'downloadAllFiles'])
        ->name('admin.orders.files.download-all');
});
