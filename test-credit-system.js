#!/usr/bin/env node

/**
 * Credit System Test Script
 * This script tests the credit system functionality without running the full application
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Credit System Verification Test');
console.log('=====================================\n');

// Test 1: Check if admin users page has credit column
function testAdminCreditColumn() {
    console.log('1. Testing Admin Users Page Credit Column...');
    
    try {
        const userResourcePath = path.join(__dirname, 'backend/app/Filament/Resources/UserResource.php');
        const content = fs.readFileSync(userResourcePath, 'utf8');
        
        // Check for credit column implementation
        const hasCreditColumn = content.includes("Tables\\Columns\\TextColumn::make('credit_balance')");
        const hasCorrectLabel = content.includes("->label('Credit')");
        const hasCurrencyFormat = content.includes("formatStateUsing");
        const isSortable = content.includes("->sortable()");
        
        console.log(`   ✅ Credit column exists: ${hasCreditColumn}`);
        console.log(`   ✅ Correct label: ${hasCorrectLabel}`);
        console.log(`   ✅ Currency formatting: ${hasCurrencyFormat}`);
        console.log(`   ✅ Sortable: ${isSortable}`);
        
        if (hasCreditColumn && hasCorrectLabel && hasCurrencyFormat && isSortable) {
            console.log('   🎉 Admin credit column: PASS\n');
            return true;
        } else {
            console.log('   ❌ Admin credit column: FAIL\n');
            return false;
        }
    } catch (error) {
        console.log(`   ❌ Error reading UserResource.php: ${error.message}\n`);
        return false;
    }
}

// Test 2: Check User model credit functionality
function testUserModelCredit() {
    console.log('2. Testing User Model Credit Functionality...');
    
    try {
        const userModelPath = path.join(__dirname, 'backend/app/Models/User.php');
        const content = fs.readFileSync(userModelPath, 'utf8');
        
        const hasCreditBalance = content.includes("'credit_balance'");
        const hasAddCredits = content.includes("function addCredits");
        const hasDeductCredits = content.includes("function deductCredits");
        const hasCreditsCheck = content.includes("function hasCredits");
        const hasCreditTransactions = content.includes("creditTransactions");
        
        console.log(`   ✅ Credit balance field: ${hasCreditBalance}`);
        console.log(`   ✅ Add credits method: ${hasAddCredits}`);
        console.log(`   ✅ Deduct credits method: ${hasDeductCredits}`);
        console.log(`   ✅ Has credits check: ${hasCreditsCheck}`);
        console.log(`   ✅ Credit transactions relation: ${hasCreditTransactions}`);
        
        if (hasCreditBalance && hasAddCredits && hasDeductCredits && hasCreditsCheck && hasCreditTransactions) {
            console.log('   🎉 User model credit functionality: PASS\n');
            return true;
        } else {
            console.log('   ❌ User model credit functionality: FAIL\n');
            return false;
        }
    } catch (error) {
        console.log(`   ❌ Error reading User.php: ${error.message}\n`);
        return false;
    }
}

// Test 3: Check Credit API endpoints
function testCreditAPI() {
    console.log('3. Testing Credit API Endpoints...');
    
    try {
        const apiRoutesPath = path.join(__dirname, 'backend/routes/api.php');
        const content = fs.readFileSync(apiRoutesPath, 'utf8');
        
        const hasCreditRoutes = content.includes("Route::prefix('credit')");
        const hasBalanceRoute = content.includes("'/balance'");
        const hasPackagesRoute = content.includes("'/packages'");
        const hasTransactionsRoute = content.includes("'/transactions'");
        const hasStatisticsRoute = content.includes("'/statistics'");
        
        console.log(`   ✅ Credit routes group: ${hasCreditRoutes}`);
        console.log(`   ✅ Balance endpoint: ${hasBalanceRoute}`);
        console.log(`   ✅ Packages endpoint: ${hasPackagesRoute}`);
        console.log(`   ✅ Transactions endpoint: ${hasTransactionsRoute}`);
        console.log(`   ✅ Statistics endpoint: ${hasStatisticsRoute}`);
        
        if (hasCreditRoutes && hasBalanceRoute && hasPackagesRoute && hasTransactionsRoute && hasStatisticsRoute) {
            console.log('   🎉 Credit API endpoints: PASS\n');
            return true;
        } else {
            console.log('   ❌ Credit API endpoints: FAIL\n');
            return false;
        }
    } catch (error) {
        console.log(`   ❌ Error reading api.php: ${error.message}\n`);
        return false;
    }
}

// Test 4: Check Frontend Credit Components
function testFrontendCreditComponents() {
    console.log('4. Testing Frontend Credit Components...');
    
    try {
        const creditPagePath = path.join(__dirname, 'frontend/src/pages/dashboard/Credit.tsx');
        const creditBalancePath = path.join(__dirname, 'frontend/src/components/credit/CreditBalance.tsx');
        const creditPackagesPath = path.join(__dirname, 'frontend/src/components/credit/CreditPackages.tsx');
        const transactionHistoryPath = path.join(__dirname, 'frontend/src/components/credit/TransactionHistory.tsx');
        
        const hasCreditPage = fs.existsSync(creditPagePath);
        const hasCreditBalance = fs.existsSync(creditBalancePath);
        const hasCreditPackages = fs.existsSync(creditPackagesPath);
        const hasTransactionHistory = fs.existsSync(transactionHistoryPath);
        
        console.log(`   ✅ Credit page component: ${hasCreditPage}`);
        console.log(`   ✅ Credit balance component: ${hasCreditBalance}`);
        console.log(`   ✅ Credit packages component: ${hasCreditPackages}`);
        console.log(`   ✅ Transaction history component: ${hasTransactionHistory}`);
        
        if (hasCreditPage && hasCreditBalance && hasCreditPackages && hasTransactionHistory) {
            console.log('   🎉 Frontend credit components: PASS\n');
            return true;
        } else {
            console.log('   ❌ Frontend credit components: FAIL\n');
            return false;
        }
    } catch (error) {
        console.log(`   ❌ Error checking frontend components: ${error.message}\n`);
        return false;
    }
}

// Test 5: Check Credit Service
function testCreditService() {
    console.log('5. Testing Credit Service...');
    
    try {
        const creditServicePath = path.join(__dirname, 'frontend/src/services/creditService.ts');
        const content = fs.readFileSync(creditServicePath, 'utf8');
        
        const hasGetBalance = content.includes("getBalance()");
        const hasGetPackages = content.includes("getPackages()");
        const hasGetTransactions = content.includes("getTransactions(");
        const hasGetStatistics = content.includes("getStatistics()");
        const hasCreatePayment = content.includes("createPayment(");
        const hasFormatCredits = content.includes("formatCredits(");
        const hasFormatCurrency = content.includes("formatCurrency(");
        
        console.log(`   ✅ Get balance method: ${hasGetBalance}`);
        console.log(`   ✅ Get packages method: ${hasGetPackages}`);
        console.log(`   ✅ Get transactions method: ${hasGetTransactions}`);
        console.log(`   ✅ Get statistics method: ${hasGetStatistics}`);
        console.log(`   ✅ Create payment method: ${hasCreatePayment}`);
        console.log(`   ✅ Format credits method: ${hasFormatCredits}`);
        console.log(`   ✅ Format currency method: ${hasFormatCurrency}`);
        
        if (hasGetBalance && hasGetPackages && hasGetTransactions && hasGetStatistics && hasCreatePayment) {
            console.log('   🎉 Credit service: PASS\n');
            return true;
        } else {
            console.log('   ❌ Credit service: FAIL\n');
            return false;
        }
    } catch (error) {
        console.log(`   ❌ Error reading creditService.ts: ${error.message}\n`);
        return false;
    }
}

// Test 6: Check Database Migrations
function testDatabaseMigrations() {
    console.log('6. Testing Database Migrations...');
    
    try {
        const migrationsDir = path.join(__dirname, 'backend/database/migrations');
        const files = fs.readdirSync(migrationsDir);
        
        const hasCreditPackagesMigration = files.some(file => file.includes('credit_packages'));
        const hasCreditTransactionsMigration = files.some(file => file.includes('credit_transactions'));
        const hasCreditBalanceMigration = files.some(file => file.includes('credit_balance'));
        
        console.log(`   ✅ Credit packages migration: ${hasCreditPackagesMigration}`);
        console.log(`   ✅ Credit transactions migration: ${hasCreditTransactionsMigration}`);
        console.log(`   ✅ Credit balance migration: ${hasCreditBalanceMigration}`);
        
        if (hasCreditPackagesMigration && hasCreditTransactionsMigration && hasCreditBalanceMigration) {
            console.log('   🎉 Database migrations: PASS\n');
            return true;
        } else {
            console.log('   ❌ Database migrations: FAIL\n');
            return false;
        }
    } catch (error) {
        console.log(`   ❌ Error checking migrations: ${error.message}\n`);
        return false;
    }
}

// Run all tests
function runAllTests() {
    const results = [
        testAdminCreditColumn(),
        testUserModelCredit(),
        testCreditAPI(),
        testFrontendCreditComponents(),
        testCreditService(),
        testDatabaseMigrations()
    ];
    
    const passedTests = results.filter(result => result).length;
    const totalTests = results.length;
    
    console.log('📊 Test Summary');
    console.log('================');
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${totalTests - passedTests}`);
    console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%\n`);
    
    if (passedTests === totalTests) {
        console.log('🎉 All tests passed! Credit system is properly implemented.');
    } else {
        console.log('⚠️  Some tests failed. Please check the implementation.');
    }
}

// Run the tests
runAllTests();
