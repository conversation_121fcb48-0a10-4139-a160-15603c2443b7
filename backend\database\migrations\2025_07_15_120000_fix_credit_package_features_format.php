<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\CreditPackage;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing credit packages to use the correct features format
        $packages = CreditPackage::all();
        
        foreach ($packages as $package) {
            if (!empty($package->features) && is_array($package->features)) {
                $updatedFeatures = [];
                
                foreach ($package->features as $feature) {
                    // If it's already in the correct format (array with 'feature' key), keep it
                    if (is_array($feature) && isset($feature['feature'])) {
                        $updatedFeatures[] = $feature;
                    }
                    // If it's a string (old format), convert it to the new format
                    elseif (is_string($feature)) {
                        $updatedFeatures[] = ['feature' => $feature];
                    }
                }
                
                // Update the package with the corrected features format
                $package->update(['features' => $updatedFeatures]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Convert back to simple string array format
        $packages = CreditPackage::all();
        
        foreach ($packages as $package) {
            if (!empty($package->features) && is_array($package->features)) {
                $updatedFeatures = [];
                
                foreach ($package->features as $feature) {
                    // If it's in the new format (array with 'feature' key), extract the string
                    if (is_array($feature) && isset($feature['feature'])) {
                        $updatedFeatures[] = $feature['feature'];
                    }
                    // If it's already a string, keep it
                    elseif (is_string($feature)) {
                        $updatedFeatures[] = $feature;
                    }
                }
                
                // Update the package with the simple string array format
                $package->update(['features' => $updatedFeatures]);
            }
        }
    }
};
