<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PrintingOrderResource\Pages;
use App\Filament\Resources\PrintingOrderResource\RelationManagers;
use App\Models\PrintingOrder;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\SelectFilter;

class PrintingOrderResource extends Resource
{
    protected static ?string $model = PrintingOrder::class;

    protected static ?string $navigationIcon = 'heroicon-o-shopping-bag';

    protected static ?string $navigationGroup = 'Printing Services';

    protected static ?int $navigationSort = 3;

    protected static ?string $recordTitleAttribute = 'order_number';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Order Information')
                    ->schema([
                        Forms\Components\TextInput::make('order_number')
                            ->required()
                            ->maxLength(255)
                            ->disabled()
                            ->dehydrated(),

                        Forms\Components\Select::make('user_id')
                            ->label('Customer')
                            ->options(User::all()->pluck('name', 'id'))
                            ->required()
                            ->searchable(),

                        Forms\Components\Select::make('status')
                            ->options(PrintingOrder::getStatuses())
                            ->required()
                            ->default(PrintingOrder::STATUS_PENDING),

                        Forms\Components\Select::make('payment_status')
                            ->options(PrintingOrder::getPaymentStatuses())
                            ->required()
                            ->default(PrintingOrder::PAYMENT_PENDING),
                    ])->columns(2),

                Forms\Components\Section::make('Pricing')
                    ->schema([
                        Forms\Components\TextInput::make('total_amount')
                            ->required()
                            ->numeric()
                            ->prefix('RM')
                            ->step(0.01),

                        Forms\Components\TextInput::make('payment_method')
                            ->maxLength(255),

                        Forms\Components\TextInput::make('payment_reference')
                            ->maxLength(255),
                    ])->columns(3),

                Forms\Components\Section::make('Delivery')
                    ->schema([
                        Forms\Components\TextInput::make('delivery_method')
                            ->maxLength(255),

                        Forms\Components\KeyValue::make('delivery_address')
                            ->keyLabel('Field')
                            ->valueLabel('Value'),
                    ])->columns(1),

                Forms\Components\Section::make('Dates')
                    ->schema([
                        Forms\Components\DatePicker::make('estimated_completion_date'),

                        Forms\Components\DateTimePicker::make('completed_at')
                            ->disabled()
                            ->dehydrated(),

                        Forms\Components\DateTimePicker::make('shipped_at')
                            ->disabled()
                            ->dehydrated(),
                    ])->columns(3),

                Forms\Components\Section::make('Notes')
                    ->schema([
                        Forms\Components\Textarea::make('special_instructions')
                            ->label('Customer Instructions')
                            ->rows(3),

                        Forms\Components\Textarea::make('notes')
                            ->label('Admin Notes')
                            ->rows(3),
                    ])->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('order_number')
                    ->searchable()
                    ->sortable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('Customer')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'secondary' => PrintingOrder::STATUS_PENDING,
                        'warning' => PrintingOrder::STATUS_CONFIRMED,
                        'primary' => PrintingOrder::STATUS_IN_PRODUCTION,
                        'info' => PrintingOrder::STATUS_QUALITY_CHECK,
                        'success' => PrintingOrder::STATUS_COMPLETED,
                        'success' => PrintingOrder::STATUS_SHIPPED,
                        'success' => PrintingOrder::STATUS_DELIVERED,
                        'danger' => PrintingOrder::STATUS_CANCELLED,
                    ])
                    ->formatStateUsing(fn (string $state): string => PrintingOrder::getStatuses()[$state] ?? $state),

                Tables\Columns\BadgeColumn::make('payment_status')
                    ->colors([
                        'warning' => PrintingOrder::PAYMENT_PENDING,
                        'success' => PrintingOrder::PAYMENT_PAID,
                        'danger' => PrintingOrder::PAYMENT_FAILED,
                        'secondary' => PrintingOrder::PAYMENT_REFUNDED,
                    ])
                    ->formatStateUsing(fn (string $state): string => PrintingOrder::getPaymentStatuses()[$state] ?? $state),

                Tables\Columns\TextColumn::make('total_amount')
                    ->money('MYR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('items_count')
                    ->counts('items')
                    ->label('Items'),

                Tables\Columns\TextColumn::make('files_count')
                    ->counts('files')
                    ->label('Files'),

                Tables\Columns\TextColumn::make('estimated_completion_date')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options(PrintingOrder::getStatuses())
                    ->multiple(),

                SelectFilter::make('payment_status')
                    ->options(PrintingOrder::getPaymentStatuses())
                    ->multiple(),

                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from'),
                        Forms\Components\DatePicker::make('created_until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ], layout: FiltersLayout::AboveContent)
            ->filtersFormColumns(3)
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('download_files')
                    ->label('Download Files')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('info')
                    ->url(fn (PrintingOrder $record): string =>
                        route('admin.orders.files.download-all', ['orderId' => $record->id])
                    )
                    ->openUrlInNewTab()
                    ->visible(fn (PrintingOrder $record): bool => $record->files()->count() > 0),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\FilesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPrintingOrders::route('/'),
            'create' => Pages\CreatePrintingOrder::route('/create'),
            'view' => Pages\ViewPrintingOrder::route('/{record}'),
            'edit' => Pages\EditPrintingOrder::route('/{record}/edit'),
        ];
    }
}
