<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PrintingProductResource\Pages;
use App\Filament\Resources\PrintingProductResource\RelationManagers;
use App\Models\PrintingProduct;
use App\Models\PrintingCategory;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;

class PrintingProductResource extends Resource
{
    protected static ?string $model = PrintingProduct::class;

    protected static ?string $navigationIcon = 'heroicon-o-cube';

    protected static ?string $navigationGroup = 'Printing Services';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Product Information')
                    ->schema([
                        Forms\Components\Select::make('printing_category_id')
                            ->label('Category')
                            ->options(PrintingCategory::active()->pluck('name', 'id'))
                            ->required()
                            ->searchable(),

                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->live(onBlur: true)
                            ->afterStateUpdated(fn (string $context, $state, Forms\Set $set) =>
                                $context === 'create' ? $set('slug', Str::slug($state)) : null
                            ),

                        Forms\Components\TextInput::make('slug')
                            ->required()
                            ->maxLength(255)
                            ->unique(PrintingProduct::class, 'slug', ignoreRecord: true)
                            ->rules(['alpha_dash']),

                        Forms\Components\Textarea::make('description')
                            ->maxLength(1000)
                            ->rows(3),

                        Forms\Components\FileUpload::make('image')
                            ->image()
                            ->directory('printing-products')
                            ->visibility('public'),
                    ])->columns(2),

                Forms\Components\Section::make('Pricing & Quantity')
                    ->schema([
                        Forms\Components\TextInput::make('base_price')
                            ->required()
                            ->numeric()
                            ->prefix('RM')
                            ->minValue(0)
                            ->step(0.01),

                        Forms\Components\TextInput::make('min_quantity')
                            ->required()
                            ->numeric()
                            ->default(1)
                            ->minValue(1),

                        Forms\Components\TextInput::make('max_quantity')
                            ->numeric()
                            ->minValue(1),

                        Forms\Components\TextInput::make('production_time_days')
                            ->required()
                            ->numeric()
                            ->default(3)
                            ->minValue(1)
                            ->suffix('days'),
                    ])->columns(2),

                Forms\Components\Section::make('Specifications')
                    ->schema([
                        Forms\Components\KeyValue::make('specifications')
                            ->keyLabel('Specification')
                            ->valueLabel('Value')
                            ->addActionLabel('Add specification'),
                    ]),

                Forms\Components\Section::make('Options & Pricing')
                    ->schema([
                        Forms\Components\Textarea::make('options')
                            ->label('Options (JSON)')
                            ->helperText('Enter pricing options and configurations in JSON format')
                            ->rows(10)
                            ->placeholder('{"quantity_pricing": [{"min_quantity": 100, "price_per_unit": 0.25}]}'),
                    ]),

                Forms\Components\Section::make('Settings')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->default(true),

                        Forms\Components\TextInput::make('sort_order')
                            ->numeric()
                            ->default(0)
                            ->minValue(0),
                    ])->columns(2),

                Forms\Components\Section::make('SEO')
                    ->schema([
                        Forms\Components\TextInput::make('meta_title')
                            ->maxLength(255),

                        Forms\Components\Textarea::make('meta_description')
                            ->maxLength(500)
                            ->rows(2),
                    ])->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image')
                    ->size(50),

                Tables\Columns\TextColumn::make('category.name')
                    ->label('Category')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('slug')
                    ->searchable()
                    ->copyable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('base_price')
                    ->money('MYR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('min_quantity')
                    ->sortable(),

                Tables\Columns\TextColumn::make('production_time_days')
                    ->suffix(' days')
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('sort_order')
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('printing_category_id')
                    ->label('Category')
                    ->options(PrintingCategory::active()->pluck('name', 'id'))
                    ->searchable(),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active')
                    ->boolean()
                    ->trueLabel('Active only')
                    ->falseLabel('Inactive only')
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sort_order');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPrintingProducts::route('/'),
            'create' => Pages\CreatePrintingProduct::route('/create'),
            'edit' => Pages\EditPrintingProduct::route('/{record}/edit'),
        ];
    }
}
