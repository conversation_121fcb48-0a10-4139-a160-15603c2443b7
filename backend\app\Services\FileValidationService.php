<?php

namespace App\Services;

use App\Models\FileUploadSetting;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;

class FileValidationService
{
    /**
     * Validate an uploaded file against current settings
     */
    public function validateFile(UploadedFile $file): array
    {
        $errors = [];
        $warnings = [];

        // Validate file type
        $typeValidation = $this->validateFileType($file);
        if (!$typeValidation['valid']) {
            $errors[] = $typeValidation['message'];
        }

        // Validate file size
        $sizeValidation = $this->validateFileSize($file);
        if (!$sizeValidation['valid']) {
            $errors[] = $sizeValidation['message'];
        }

        // Validate dimensions for images
        if ($this->isImage($file)) {
            $dimensionValidation = $this->validateDimensions($file);
            if (!$dimensionValidation['valid']) {
                if ($dimensionValidation['type'] === 'error') {
                    $errors[] = $dimensionValidation['message'];
                } else {
                    $warnings[] = $dimensionValidation['message'];
                }
            }

            // Validate DPI for images
            $dpiValidation = $this->validateDPI($file);
            if (!$dpiValidation['valid']) {
                if ($dpiValidation['type'] === 'error') {
                    $errors[] = $dpiValidation['message'];
                } else {
                    $warnings[] = $dpiValidation['message'];
                }
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings,
        ];
    }

    /**
     * Validate file type against allowed types
     */
    private function validateFileType(UploadedFile $file): array
    {
        $allowedTypes = FileUploadSetting::getTyped('allowed_file_types', ['pdf', 'png', 'jpg', 'jpeg']);

        // Ensure allowed_file_types is always an array
        if (!is_array($allowedTypes)) {
            if (is_string($allowedTypes)) {
                $allowedTypes = json_decode($allowedTypes, true) ?: ['pdf', 'png', 'jpg', 'jpeg'];
            } else {
                $allowedTypes = ['pdf', 'png', 'jpg', 'jpeg'];
            }
        }

        $fileExtension = strtolower($file->getClientOriginalExtension());

        if (!in_array($fileExtension, $allowedTypes)) {
            return [
                'valid' => false,
                'message' => "File type '{$fileExtension}' is not allowed. Allowed types: " . implode(', ', $allowedTypes)
            ];
        }

        return ['valid' => true];
    }

    /**
     * Validate file size against maximum allowed size
     */
    private function validateFileSize(UploadedFile $file): array
    {
        $maxSizeMB = FileUploadSetting::get('max_file_size_mb', 50);
        $maxSizeBytes = $maxSizeMB * 1024 * 1024;

        if ($file->getSize() > $maxSizeBytes) {
            return [
                'valid' => false,
                'message' => "File size ({$this->formatFileSize($file->getSize())}) exceeds maximum allowed size ({$maxSizeMB}MB)"
            ];
        }

        return ['valid' => true];
    }

    /**
     * Validate image dimensions
     */
    private function validateDimensions(UploadedFile $file): array
    {
        if (!FileUploadSetting::get('enable_dimension_validation', true)) {
            return ['valid' => true];
        }

        try {
            $imageSize = getimagesize($file->getPathname());
            if (!$imageSize) {
                return ['valid' => true]; // Skip validation if we can't get dimensions
            }

            $width = $imageSize[0];
            $height = $imageSize[1];

            $minWidth = FileUploadSetting::get('min_width_px', 100);
            $minHeight = FileUploadSetting::get('min_height_px', 100);
            $maxWidth = FileUploadSetting::get('max_width_px', 10000);
            $maxHeight = FileUploadSetting::get('max_height_px', 10000);

            if ($width < $minWidth || $height < $minHeight) {
                return [
                    'valid' => false,
                    'type' => 'error',
                    'message' => "Image dimensions ({$width}x{$height}px) are below minimum requirements ({$minWidth}x{$minHeight}px)"
                ];
            }

            if ($width > $maxWidth || $height > $maxHeight) {
                return [
                    'valid' => false,
                    'type' => 'error',
                    'message' => "Image dimensions ({$width}x{$height}px) exceed maximum allowed size ({$maxWidth}x{$maxHeight}px)"
                ];
            }

        } catch (\Exception $e) {
            Log::warning('Failed to validate image dimensions: ' . $e->getMessage());
        }

        return ['valid' => true];
    }

    /**
     * Validate DPI for images
     */
    private function validateDPI(UploadedFile $file): array
    {
        if (!FileUploadSetting::get('enable_dpi_validation', true)) {
            return ['valid' => true];
        }

        $dpi = $this->extractDPI($file);
        if (!$dpi) {
            return ['valid' => true]; // Skip validation if we can't detect DPI
        }

        $minDPI = FileUploadSetting::get('min_dpi_requirement', 300);
        $warningThreshold = FileUploadSetting::get('dpi_warning_threshold', 150);

        if ($dpi < $warningThreshold) {
            return [
                'valid' => false,
                'type' => 'error',
                'message' => "Image DPI ({$dpi}) is too low for print quality. Minimum required: {$minDPI} DPI"
            ];
        }

        if ($dpi < $minDPI) {
            return [
                'valid' => false,
                'type' => 'warning',
                'message' => "Image DPI ({$dpi}) is below recommended print quality ({$minDPI} DPI)"
            ];
        }

        return ['valid' => true];
    }

    /**
     * Extract DPI from image file
     */
    private function extractDPI(UploadedFile $file): ?int
    {
        if (!FileUploadSetting::get('auto_dpi_detection', true)) {
            return null;
        }

        try {
            // Try to get DPI from EXIF data
            if (function_exists('exif_read_data') && in_array($file->getMimeType(), ['image/jpeg', 'image/tiff'])) {
                $exif = @exif_read_data($file->getPathname());
                if ($exif && isset($exif['XResolution'])) {
                    return $this->parseDpi($exif['XResolution']);
                }
            }

            // Try to get DPI from image info
            $imageSize = getimagesize($file->getPathname());
            if ($imageSize && isset($imageSize['channels'])) {
                // This is a basic estimation - real DPI detection would need more sophisticated methods
                return $this->estimateDpi($imageSize);
            }

        } catch (\Exception $e) {
            Log::warning('Failed to extract DPI: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Parse DPI value from EXIF data
     */
    private function parseDpi($dpiValue): ?int
    {
        if (is_string($dpiValue) && strpos($dpiValue, '/') !== false) {
            $parts = explode('/', $dpiValue);
            if (count($parts) === 2 && $parts[1] != 0) {
                return (int) ($parts[0] / $parts[1]);
            }
        }

        return is_numeric($dpiValue) ? (int) $dpiValue : null;
    }

    /**
     * Estimate DPI based on image dimensions (basic estimation)
     */
    private function estimateDpi(array $imageSize): int
    {
        $width = $imageSize[0];
        $height = $imageSize[1];

        // Basic estimation based on common print sizes
        // This is a rough estimate and may not be accurate
        if ($width >= 2400 && $height >= 3000) {
            return 300; // Likely high-res print quality
        } elseif ($width >= 1200 && $height >= 1500) {
            return 150; // Medium quality
        } else {
            return 72; // Web quality
        }
    }

    /**
     * Check if file is an image
     */
    private function isImage(UploadedFile $file): bool
    {
        return in_array($file->getMimeType(), [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/tiff',
            'image/svg+xml',
            'image/webp'
        ]);
    }

    /**
     * Format file size for display
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Validate total upload size for an order
     */
    public function validateTotalUploadSize(array $files, int $orderId = null): array
    {
        $maxTotalSizeMB = FileUploadSetting::get('max_total_upload_size_mb', 200);
        $maxTotalSizeBytes = $maxTotalSizeMB * 1024 * 1024;

        $totalSize = 0;
        foreach ($files as $file) {
            $totalSize += $file->getSize();
        }

        // TODO: Add existing files size for the order if $orderId is provided

        if ($totalSize > $maxTotalSizeBytes) {
            return [
                'valid' => false,
                'message' => "Total upload size ({$this->formatFileSize($totalSize)}) exceeds maximum allowed ({$maxTotalSizeMB}MB)"
            ];
        }

        return ['valid' => true];
    }

    /**
     * Validate number of files per order
     */
    public function validateFileCount(array $files, int $orderId = null): array
    {
        $maxFiles = FileUploadSetting::get('max_files_per_order', 10);
        $fileCount = count($files);

        // TODO: Add existing files count for the order if $orderId is provided

        if ($fileCount > $maxFiles) {
            return [
                'valid' => false,
                'message' => "Number of files ({$fileCount}) exceeds maximum allowed ({$maxFiles})"
            ];
        }

        return ['valid' => true];
    }
}
