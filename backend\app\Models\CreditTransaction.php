<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CreditTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'credit_package_id',
        'type',
        'credit_amount',
        'amount_paid',
        'payment_method',
        'payment_reference',
        'payment_status',
        'description',
        'metadata',
        'processed_at',
    ];

    protected function casts(): array
    {
        return [
            'amount_paid' => 'decimal:2',
            'metadata' => 'array',
            'processed_at' => 'datetime',
        ];
    }

    /**
     * Get the user that owns the transaction
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the credit package associated with the transaction
     */
    public function creditPackage()
    {
        return $this->belongsTo(CreditPackage::class);
    }

    /**
     * Scope for completed transactions
     */
    public function scopeCompleted($query)
    {
        return $query->where('payment_status', 'completed');
    }

    /**
     * Scope for pending transactions
     */
    public function scopePending($query)
    {
        return $query->where('payment_status', 'pending');
    }

    /**
     * Scope for failed transactions
     */
    public function scopeFailed($query)
    {
        return $query->where('payment_status', 'failed');
    }

    /**
     * Scope for credit transactions (positive amounts)
     */
    public function scopeCredits($query)
    {
        return $query->where('credit_amount', '>', 0);
    }

    /**
     * Scope for debit transactions (negative amounts)
     */
    public function scopeDebits($query)
    {
        return $query->where('credit_amount', '<', 0);
    }

    /**
     * Get formatted amount paid
     */
    public function getFormattedAmountPaidAttribute()
    {
        return $this->amount_paid ? 'RM ' . number_format($this->amount_paid, 2) : null;
    }

    /**
     * Check if transaction is a credit (positive amount)
     */
    public function getIsCreditAttribute()
    {
        return $this->credit_amount > 0;
    }

    /**
     * Check if transaction is a debit (negative amount)
     */
    public function getIsDebitAttribute()
    {
        return $this->credit_amount < 0;
    }
}
