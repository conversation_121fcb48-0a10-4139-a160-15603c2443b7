<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PrintingOrder;
use App\Models\PrintingProduct;
use App\Models\OrderItem;
use App\Models\OrderFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class OrderController extends Controller
{
    /**
     * Get user's orders
     */
    public function index(Request $request)
    {
        $orders = PrintingOrder::with(['items.product', 'files'])
            ->where('user_id', $request->user()->id)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return response()->json([
            'success' => true,
            'data' => $orders->items(),
            'meta' => [
                'current_page' => $orders->currentPage(),
                'last_page' => $orders->lastPage(),
                'per_page' => $orders->perPage(),
                'total' => $orders->total(),
                'from' => $orders->firstItem(),
                'to' => $orders->lastItem(),
            ]
        ]);
    }

    /**
     * Get single order details
     */
    public function show(Request $request, $id)
    {
        $order = PrintingOrder::with(['items.product.category', 'files.uploader'])
            ->where('user_id', $request->user()->id)
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $order
        ]);
    }

    /**
     * Create new order
     */
    public function store(Request $request)
    {
        $request->validate([
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:printing_products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.specifications' => 'array',
            'items.*.selected_options' => 'array',
            'items.*.notes' => 'nullable|string',
            'special_instructions' => 'nullable|string',
            'delivery_address' => 'nullable|array',
            'delivery_method' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            // Calculate total amount
            $totalAmount = 0;
            $orderItems = [];

            foreach ($request->items as $itemData) {
                $product = PrintingProduct::findOrFail($itemData['product_id']);

                // Validate quantity limits
                if ($itemData['quantity'] < $product->min_quantity) {
                    throw new \Exception("Minimum quantity for {$product->name} is {$product->min_quantity}");
                }

                if ($product->max_quantity && $itemData['quantity'] > $product->max_quantity) {
                    throw new \Exception("Maximum quantity for {$product->name} is {$product->max_quantity}");
                }

                $unitPrice = $product->calculatePrice($itemData['quantity'], $itemData['selected_options'] ?? []) / $itemData['quantity'];
                $totalPrice = $unitPrice * $itemData['quantity'];
                $totalAmount += $totalPrice;

                $orderItems[] = [
                    'product' => $product,
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $unitPrice,
                    'total_price' => $totalPrice,
                    'specifications' => $itemData['specifications'] ?? [],
                    'selected_options' => $itemData['selected_options'] ?? [],
                    'notes' => $itemData['notes'] ?? null,
                ];
            }

            // Create order
            $order = PrintingOrder::create([
                'user_id' => $request->user()->id,
                'total_amount' => $totalAmount,
                'status' => PrintingOrder::STATUS_PENDING,
                'payment_status' => PrintingOrder::PAYMENT_PENDING,
                'special_instructions' => $request->special_instructions,
                'delivery_address' => $request->delivery_address,
                'delivery_method' => $request->delivery_method,
                'estimated_completion_date' => now()->addDays(7), // Default 7 days
            ]);

            // Create order items
            foreach ($orderItems as $itemData) {
                OrderItem::create([
                    'printing_order_id' => $order->id,
                    'printing_product_id' => $itemData['product']->id,
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $itemData['unit_price'],
                    'total_price' => $itemData['total_price'],
                    'specifications' => $itemData['specifications'],
                    'selected_options' => $itemData['selected_options'],
                    'notes' => $itemData['notes'],
                ]);
            }

            DB::commit();

            // Load the order with relationships
            $order->load(['items.product', 'files']);

            return response()->json([
                'success' => true,
                'message' => 'Order created successfully',
                'data' => $order
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Update order status (admin only)
     */
    public function updateStatus(Request $request, $id)
    {
        // This would typically be restricted to admin users
        $request->validate([
            'status' => ['required', Rule::in(array_keys(PrintingOrder::getStatuses()))],
            'notes' => 'nullable|string',
        ]);

        $order = PrintingOrder::findOrFail($id);

        $order->update([
            'status' => $request->status,
            'notes' => $request->notes,
            'completed_at' => $request->status === PrintingOrder::STATUS_COMPLETED ? now() : null,
            'shipped_at' => $request->status === PrintingOrder::STATUS_SHIPPED ? now() : null,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Order status updated successfully',
            'data' => $order
        ]);
    }

    /**
     * Cancel order
     */
    public function cancel(Request $request, $id)
    {
        $order = PrintingOrder::where('user_id', $request->user()->id)
            ->where('status', PrintingOrder::STATUS_PENDING)
            ->findOrFail($id);

        $order->update([
            'status' => PrintingOrder::STATUS_CANCELLED
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Order cancelled successfully',
            'data' => $order
        ]);
    }

    /**
     * Reorder - create new order based on existing order
     */
    public function reorder(Request $request, $id)
    {
        $originalOrder = PrintingOrder::with('items.product')
            ->where('user_id', $request->user()->id)
            ->findOrFail($id);

        try {
            DB::beginTransaction();

            // Create new order
            $newOrder = PrintingOrder::create([
                'user_id' => $request->user()->id,
                'total_amount' => $originalOrder->total_amount,
                'special_instructions' => $originalOrder->special_instructions,
                'delivery_address' => $originalOrder->delivery_address,
                'delivery_method' => $originalOrder->delivery_method,
                'estimated_completion_date' => now()->addDays(7),
            ]);

            // Copy order items
            foreach ($originalOrder->items as $item) {
                OrderItem::create([
                    'printing_order_id' => $newOrder->id,
                    'printing_product_id' => $item->printing_product_id,
                    'quantity' => $item->quantity,
                    'unit_price' => $item->unit_price,
                    'total_price' => $item->total_price,
                    'specifications' => $item->specifications,
                    'selected_options' => $item->selected_options,
                    'notes' => $item->notes,
                ]);
            }

            DB::commit();

            $newOrder->load(['items.product', 'files']);

            return response()->json([
                'success' => true,
                'message' => 'Order recreated successfully',
                'data' => $newOrder
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to recreate order: ' . $e->getMessage()
            ], 400);
        }
    }
}
