<?php

namespace Database\Seeders;

use App\Models\CreditPackage;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CreditPackageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $packages = [
            [
                'name' => 'Starter Pack',
                'description' => 'Perfect for getting started with our platform',
                'price' => 10.00,
                'credit_amount' => 100,
                'is_active' => true,
                'sort_order' => 1,
                'features' => [
                    ['feature' => 'Basic support'],
                    ['feature' => 'Standard processing speed'],
                    ['feature' => 'Email notifications'],
                ],
            ],
            [
                'name' => 'Popular Pack',
                'description' => 'Most popular choice for regular users',
                'price' => 25.00,
                'credit_amount' => 300,
                'is_active' => true,
                'sort_order' => 2,
                'features' => [
                    ['feature' => 'Priority support'],
                    ['feature' => 'Faster processing speed'],
                    ['feature' => 'Email & SMS notifications'],
                    ['feature' => '20% bonus credits'],
                ],
            ],
            [
                'name' => 'Professional Pack',
                'description' => 'Best value for power users and businesses',
                'price' => 50.00,
                'credit_amount' => 700,
                'is_active' => true,
                'sort_order' => 3,
                'features' => [
                    ['feature' => 'Premium support'],
                    ['feature' => 'Fastest processing speed'],
                    ['feature' => 'All notification types'],
                    ['feature' => '40% bonus credits'],
                    ['feature' => 'Priority queue access'],
                ],
            ],
            [
                'name' => 'Enterprise Pack',
                'description' => 'Maximum credits for enterprise usage',
                'price' => 100.00,
                'credit_amount' => 1500,
                'is_active' => true,
                'sort_order' => 4,
                'features' => [
                    ['feature' => 'Dedicated support'],
                    ['feature' => 'Maximum processing speed'],
                    ['feature' => 'Custom integrations'],
                    ['feature' => '50% bonus credits'],
                    ['feature' => 'Priority queue access'],
                    ['feature' => 'Custom reporting'],
                ],
            ],
        ];

        foreach ($packages as $package) {
            CreditPackage::updateOrCreate(
                ['name' => $package['name']],
                $package
            );
        }
    }
}
