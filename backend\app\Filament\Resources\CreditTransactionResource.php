<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CreditTransactionResource\Pages;
use App\Filament\Resources\CreditTransactionResource\RelationManagers;
use App\Models\CreditTransaction;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CreditTransactionResource extends Resource
{
    protected static ?string $model = CreditTransaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';
    protected static ?string $navigationGroup = 'Settings';
    protected static ?string $navigationLabel = 'Credit Transactions';
    protected static ?string $modelLabel = 'Credit Transaction';
    protected static ?string $pluralModelLabel = 'Credit Transactions';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Transaction Details')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->relationship('user', 'name')
                            ->required()
                            ->searchable(),

                        Forms\Components\Select::make('credit_package_id')
                            ->relationship('creditPackage', 'name')
                            ->nullable(),

                        Forms\Components\Select::make('type')
                            ->options([
                                'purchase' => 'Purchase',
                                'usage' => 'Usage',
                                'refund' => 'Refund',
                                'bonus' => 'Bonus',
                                'adjustment' => 'Adjustment',
                            ])
                            ->required(),

                        Forms\Components\TextInput::make('credit_amount')
                            ->required()
                            ->numeric()
                            ->suffix('credits'),

                        Forms\Components\TextInput::make('amount_paid')
                            ->numeric()
                            ->prefix('RM')
                            ->step(0.01),

                        Forms\Components\Select::make('payment_method')
                            ->options([
                                'billplz' => 'Billplz',
                                'manual' => 'Manual',
                                'system' => 'System',
                            ]),

                        Forms\Components\TextInput::make('payment_reference')
                            ->maxLength(255),

                        Forms\Components\Select::make('payment_status')
                            ->options([
                                'pending' => 'Pending',
                                'completed' => 'Completed',
                                'failed' => 'Failed',
                                'refunded' => 'Refunded',
                            ])
                            ->required(),

                        Forms\Components\Textarea::make('description')
                            ->rows(3)
                            ->columnSpanFull(),

                        Forms\Components\DateTimePicker::make('processed_at'),
                    ])->columns(2),

                Forms\Components\Section::make('Additional Information')
                    ->schema([
                        Forms\Components\KeyValue::make('metadata')
                            ->label('Metadata')
                            ->helperText('Additional payment gateway data in key-value format')
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),

                Tables\Columns\TextColumn::make('user.name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'purchase' => 'success',
                        'usage' => 'warning',
                        'refund' => 'danger',
                        'bonus' => 'info',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('credit_amount')
                    ->suffix(' credits')
                    ->color(fn ($state) => $state > 0 ? 'success' : 'danger')
                    ->sortable(),

                Tables\Columns\TextColumn::make('amount_paid')
                    ->money('MYR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('payment_method')
                    ->badge(),

                Tables\Columns\TextColumn::make('payment_status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'completed' => 'success',
                        'pending' => 'warning',
                        'failed' => 'danger',
                        'refunded' => 'gray',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('processed_at')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('metadata')
                    ->label('Metadata')
                    ->formatStateUsing(function ($state) {
                        if (is_array($state) && !empty($state)) {
                            return json_encode($state, JSON_PRETTY_PRINT);
                        }
                        return 'No metadata';
                    })
                    ->limit(50)
                    ->tooltip(function ($state) {
                        if (is_array($state) && !empty($state)) {
                            return json_encode($state, JSON_PRETTY_PRINT);
                        }
                        return null;
                    })
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'purchase' => 'Purchase',
                        'usage' => 'Usage',
                        'refund' => 'Refund',
                        'bonus' => 'Bonus',
                        'adjustment' => 'Adjustment',
                    ]),

                Tables\Filters\SelectFilter::make('payment_status')
                    ->options([
                        'pending' => 'Pending',
                        'completed' => 'Completed',
                        'failed' => 'Failed',
                        'refunded' => 'Refunded',
                    ]),

                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from'),
                        Forms\Components\DatePicker::make('created_until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCreditTransactions::route('/'),
            'view' => Pages\ViewCreditTransaction::route('/{record}'),
            'edit' => Pages\EditCreditTransaction::route('/{record}/edit'),
        ];
    }
}
